<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Flexiwind\Core\FileEditor;

echo "=== FileEditor Class Demonstration ===\n\n";

// Create a temporary directory for our demo
$demoDir = __DIR__ . '/temp_demo';
if (!is_dir($demoDir)) {
    mkdir($demoDir, 0755, true);
}

try {
    // Demo 1: Create a new file with content
    echo "1. Creating a new file with content...\n";
    $testFile = $demoDir . '/test.txt';
    FileEditor::updateFileContent($testFile, "Hello World!\nThis is a test file.");
    echo "✅ File created: $testFile\n";
    echo "Content:\n" . file_get_contents($testFile) . "\n\n";

    // Demo 2: Insert code at the top
    echo "2. Inserting code at the top...\n";
    FileEditor::insertCode($testFile, "// This is a header comment", 'top');
    echo "✅ Code inserted at top\n";
    echo "Content:\n" . file_get_contents($testFile) . "\n\n";

    // Demo 3: Insert code at the bottom
    echo "3. Inserting code at the bottom...\n";
    FileEditor::insertCode($testFile, "// This is a footer comment", 'bottom');
    echo "✅ Code inserted at bottom\n";
    echo "Content:\n" . file_get_contents($testFile) . "\n\n";

    // Demo 4: Insert code after a specific line
    echo "4. Inserting code after line 2...\n";
    FileEditor::insertCode($testFile, "// Inserted after line 2", 'after_line', 2);
    echo "✅ Code inserted after line 2\n";
    echo "Content:\n" . file_get_contents($testFile) . "\n\n";

    // Demo 5: Insert code using search pattern
    echo "5. Inserting code after finding 'Hello World!'...\n";
    FileEditor::insertCode($testFile, "// Found Hello World!", 'after_line', null, 'Hello World!');
    echo "✅ Code inserted after search pattern\n";
    echo "Content:\n" . file_get_contents($testFile) . "\n\n";

    // Demo 6: Append content if it doesn't exist
    echo "6. Appending content if it doesn't exist...\n";
    $gitignoreFile = $demoDir . '/.gitignore';
    FileEditor::appendIfNotExists($gitignoreFile, '.flexiwind/');
    FileEditor::appendIfNotExists($gitignoreFile, 'node_modules/');
    FileEditor::appendIfNotExists($gitignoreFile, '.flexiwind/'); // This should not be added again
    echo "✅ Content appended to .gitignore\n";
    echo "Content:\n" . file_get_contents($gitignoreFile) . "\n\n";

    // Demo 7: Replace content in file
    echo "7. Replacing content in file...\n";
    FileEditor::replaceInFile($testFile, 'Hello World!', 'Hello Universe!');
    echo "✅ Content replaced\n";
    echo "Content:\n" . file_get_contents($testFile) . "\n\n";

    // Demo 8: Check if file contains content
    echo "8. Checking if file contains specific content...\n";
    $containsUniverse = FileEditor::fileContains($testFile, 'Universe');
    $containsWorld = FileEditor::fileContains($testFile, 'World');
    echo "Contains 'Universe': " . ($containsUniverse ? 'Yes' : 'No') . "\n";
    echo "Contains 'World': " . ($containsWorld ? 'Yes' : 'No') . "\n\n";

    echo "=== All demos completed successfully! ===\n";

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
} finally {
    // Clean up demo files
    echo "\nCleaning up demo files...\n";
    if (is_dir($demoDir)) {
        $files = glob($demoDir . '/*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        rmdir($demoDir);
    }
    echo "✅ Cleanup completed\n";
}
