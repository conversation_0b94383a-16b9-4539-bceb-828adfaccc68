<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Flexiwind\Core\ConfigWriter;

echo "=== UnoCSS Configuration Demonstration ===\n\n";

// Create a temporary directory for our demo
$demoDir = __DIR__ . '/temp_uno_demo';
if (!is_dir($demoDir)) {
    mkdir($demoDir, 0755, true);
}

// Copy sample vite config to demo directory
$sampleConfig = __DIR__ . '/sample_vite.config.js';
$testConfig = $demoDir . '/vite.config.js';
copy($sampleConfig, $testConfig);

echo "Original vite.config.js:\n";
echo "========================\n";
echo file_get_contents($testConfig) . "\n\n";

try {
    // Change to demo directory to simulate being in a Laravel project
    $originalDir = getcwd();
    chdir($demoDir);
    
    // Demo 1: Update UnoCSS configuration
    echo "1. Creating/updating UnoCSS configuration...\n";
    ConfigWriter::updateUnoConfig();
    echo "✅ UnoCSS config created/updated successfully!\n\n";
    
    if (file_exists('uno.config.js')) {
        echo "Generated uno.config.js:\n";
        echo "========================\n";
        echo file_get_contents('uno.config.js') . "\n\n";
    }
    
    // Demo 2: Update Vite config for UnoCSS
    echo "2. Updating Vite config with UnoCSS...\n";
    ConfigWriter::updateUnoViteConfig();
    echo "✅ Vite config updated for UnoCSS successfully!\n\n";
    
    echo "Updated vite.config.js:\n";
    echo "=======================\n";
    echo file_get_contents($testConfig) . "\n\n";
    
    // Test running it again to ensure idempotency
    echo "3. Running updates again to test idempotency...\n";
    ConfigWriter::updateUnoConfig();
    ConfigWriter::updateUnoViteConfig();
    echo "✅ Second update completed (should be no changes to vite config)\n\n";
    
    echo "Final vite.config.js (should be same as previous):\n";
    echo "==================================================\n";
    echo file_get_contents($testConfig) . "\n\n";
    
    echo "Final uno.config.js:\n";
    echo "====================\n";
    echo file_get_contents('uno.config.js') . "\n\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
} finally {
    // Restore original directory
    chdir($originalDir);
    
    // Clean up demo files
    echo "Cleaning up demo files...\n";
    if (is_dir($demoDir)) {
        $files = glob($demoDir . '/*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        rmdir($demoDir);
    }
    echo "✅ Cleanup completed\n";
}
