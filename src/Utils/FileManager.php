<?php

namespace Flexiwind\Utils;

class FileManager
{
    protected string $stubsPath;

    public function __construct()
    {
        $this->stubsPath = dirname(__DIR__, 2) . '/stubs';
    }

    /**
     * Copy an entire stub directory into target location
     */
    public function copyStubDirectory(string $stubDir, string $targetDir): void
    {
        $source = $this->stubsPath . '/' . $stubDir;

        if (!is_dir($source)) {
            throw new \RuntimeException("Stub directory not found: {$source}");
        }

        $this->recursiveCopy($source, $targetDir);
    }

    protected function recursiveCopy(string $source, string $destination): void
    {
        $dir = opendir($source);
        @mkdir($destination, 0777, true);

        while (($file = readdir($dir)) !== false) {
            if ($file === '.' || $file === '..') {
                continue;
            }

            $src = $source . '/' . $file;
            $dest = $destination . '/' . $file;

            if (is_dir($src)) {
                $this->recursiveCopy($src, $dest);
            } else {
                copy($src, $dest);
            }
        }

        closedir($dir);
    }
}
