<?php

namespace Flexiwind\Core;

class PackageInstaller
{
    public function installComposerPackage(string $packageName, bool  $isDevDep = false)
    {
        $commandDevDep = $isDevDep ? "" : "";
        exec("composer install $packageName");
    }
    public function installNpmPackage(string $packageManager, string $packageName, bool $isDevDep = false)
    {
        $commandDevDep = $isDevDep ? "-D" : "";
        exec("$packageManager install $packageName $commandDevDep");
    }
}
