<?php

namespace Flexiwind\Core;

use <PERSON>lexiwind\Core\StubStorage;

class FileGenerator
{
    public static function generateBaseFiles(string $projectType, array $answers): void
    {
        if ($projectType === 'laravel') {
            self::createLaravelFiles($answers['js'], $answers['css']);
        } else {
            self::createGenericFiles($answers['js'], $answers['css']);
        }
    }

    private static function createLaravelFiles($jsFolder, $cssFolder): void
    {
        if (!is_dir('app/Flexiwind')) {
            mkdir('app/Flexiwind', 0755, true);
        }

        file_put_contents(
            'app/Flexiwind/UiHelper.php',
            StubStorage::get('laravel.ui_helper')
        );

        file_put_contents(
            'app/Flexiwind/ButtonHelper.php',
            StubStorage::get('laravel.button_helper')
        );

        if (!is_dir('resources/js')) {
            mkdir('resources/js', 0755, true);
        }
        if (!is_dir('resources/css')) {
            mkdir('resources/css', 0755, true);
        }

        file_put_contents(
            $cssFolder . '/app.css',
            StubStorage::get('css.base')
        );

        file_put_contents(
            $jsFolder . '/js/flexilla.js',
            StubStorage::get('laravel.js.flexilla')
        );
        file_put_contents(
            $cssFolder . '/flexiwind.css',
            StubStorage::get('css.flexiwind')
        );
        file_put_contents(
            $jsFolder . '/button-styles',
            StubStorage::get('css.buttons')
        );
        file_put_contents(
            $jsFolder . '/ui-utilities.css',
            StubStorage::get('css.utilities')
        );
    }

    private static function updateLaravelLayoutConfig() {}

    private static function createGenericFiles($jsFolder, $cssFolder): void
    {
        if (!is_dir('flexiwind')) {
            mkdir('flexiwind', 0755, true);
        }

        file_put_contents(
            $jsFolder . 'flexilla.js',
            StubStorage::get('generic.js.flexilla')
        );
        file_put_contents(
            $cssFolder . '/flexiwind.css',
            StubStorage::get('generic.css.flexiwind')
        );
        file_put_contents(
            $cssFolder . '/buttons.css',
            StubStorage::get('generic.css.buttons')
        );
        file_put_contents(
            $cssFolder . '/utilities.css',
            StubStorage::get('generic.css.utilities')
        );
    }
}
