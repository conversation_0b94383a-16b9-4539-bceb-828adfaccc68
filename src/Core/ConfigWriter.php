<?php

namespace Flexiwind\Core;


use <PERSON>lexiwind\Core\FileEditor;

class ConfigWriter
{
    public static function createFlexiwindYaml(array $answers): void
    {
        $yaml = "theme: {$answers['theme']}\n";
        $yaml .= "alpine: " . ($answers['alpine'] ? 'true' : 'false') . "\n";

        file_put_contents('flexiwind.yaml', $yaml);
    }

    public static function createKeysYaml(): void
    {
        if (!is_dir('.flexiwind')) {
            mkdir('.flexiwind', 0755, true);
        }

        file_put_contents('.flexiwind/keys.yaml', "# Reserved for API keys\n");
        FileEditor::updateFileContent('.gitignore', '');
    }

    public static function updateTailwindViteConfig() {}

    public static function updateUnoConfig() {}

    public static function updateUnoViteConfig() {}
}
