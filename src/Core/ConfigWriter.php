<?php

namespace Flexiwind\Core;


use Flexiwind\Core\FileEditor;
use Flexiwind\Core\StubStorage;

class ConfigWriter
{
    public static function createFlexiwindYaml(array $answers): void
    {
        $yaml = "theme: {$answers['theme']}\n";
        $yaml .= "alpine: " . ($answers['alpine'] ? 'true' : 'false') . "\n";

        file_put_contents('flexiwind.yaml', $yaml);
    }

    public static function createKeysYaml(): void
    {
        if (!is_dir('.flexiwind')) {
            mkdir('.flexiwind', 0755, true);
        }

        file_put_contents('.flexiwind/keys.yaml', "# Reserved for API keys\n");
        FileEditor::appendIfNotExists(getcwd() . '/.gitignore', '.flexiwind/');
    }

    public static function updateTailwindViteConfig(): void
    {
        $viteConfigPath = self::findViteConfigFile();

        if (!$viteConfigPath) {
            throw new \RuntimeException("Vite config file not found. Expected vite.config.js or vite.config.ts");
        }

        // Add TailwindCSS import
        self::addTailwindImport($viteConfigPath);

        // Add TailwindCSS plugin to plugins array
        self::addTailwindPlugin($viteConfigPath);

        // Update Laravel plugin input array
        self::updateLaravelPluginInput($viteConfigPath);
    }

    public static function updateUnoConfig(): void
    {
        $unoConfigPath = self::findUnoConfigFile();

        if (!$unoConfigPath) {
            // Create new uno.config.js file
            $unoConfigPath = getcwd() . '/uno.config.js';
        }

        // Replace the entire config with our Flexiwind-optimized UnoCSS config
        $newConfig = StubStorage::get('uno.config.js');
        FileEditor::updateFileContent($unoConfigPath, $newConfig);
    }

    public static function updateUnoViteConfig(): void
    {
        $viteConfigPath = self::findViteConfigFile();

        if (!$viteConfigPath) {
            throw new \RuntimeException("Vite config file not found. Expected vite.config.js or vite.config.ts");
        }

        // Add UnoCSS import
        self::addUnoImport($viteConfigPath);

        // Add UnoCSS plugin to plugins array
        self::addUnoPlugin($viteConfigPath);

        // Update Laravel plugin input array
        self::updateLaravelPluginInput($viteConfigPath);
    }

    /**
     * Find the Vite configuration file (vite.config.js or vite.config.ts)
     *
     * @return string|null Path to the vite config file or null if not found
     */
    private static function findViteConfigFile(): ?string
    {
        $possibleFiles = [
            getcwd() . '/vite.config.js',
            getcwd() . '/vite.config.ts'
        ];

        foreach ($possibleFiles as $file) {
            if (file_exists($file)) {
                return $file;
            }
        }

        return null;
    }

    /**
     * Add TailwindCSS import to the vite config file
     *
     * @param string $viteConfigPath Path to the vite config file
     */
    private static function addTailwindImport(string $viteConfigPath): void
    {
        $tailwindImport = "import tailwindcss from '@tailwindcss/vite';";

        // Check if import already exists
        if (FileEditor::fileContains($viteConfigPath, $tailwindImport)) {
            return; // Import already exists
        }

        // Check for alternative import formats
        if (FileEditor::fileContains($viteConfigPath, "from '@tailwindcss/vite'")) {
            return; // Import already exists in some form
        }

        // Find the position to insert the import (after other imports)
        $content = file_get_contents($viteConfigPath);
        $lines = explode("\n", $content);

        $insertPosition = 0;
        foreach ($lines as $index => $line) {
            if (strpos(trim($line), 'import') === 0) {
                $insertPosition = $index + 1;
            } elseif (trim($line) === '' && $insertPosition > 0) {
                // Found empty line after imports
                break;
            } elseif (strpos(trim($line), 'export') === 0 || strpos(trim($line), 'const') === 0) {
                // Found start of actual code
                break;
            }
        }

        FileEditor::insertCode($viteConfigPath, $tailwindImport, 'after_line', $insertPosition);
    }

    /**
     * Add TailwindCSS plugin to the plugins array in vite config
     *
     * @param string $viteConfigPath Path to the vite config file
     */
    private static function addTailwindPlugin(string $viteConfigPath): void
    {
        // Check if tailwindcss() is already in the plugins array
        if (FileEditor::fileContains($viteConfigPath, 'tailwindcss()')) {
            return; // Plugin already exists
        }

        $content = file_get_contents($viteConfigPath);

        // Find the plugins array and add tailwindcss() to it
        if (preg_match('/plugins:\s*\[(.*?)\]/s', $content, $matches)) {
            $pluginsContent = trim($matches[1]);

            // Check if there are already plugins
            if ($pluginsContent !== '') {
                // Add tailwindcss() after existing plugins
                $replacement = $pluginsContent . ",\n        tailwindcss(),";
            } else {
                // Add tailwindcss() as the first plugin
                $replacement = "\n        tailwindcss(),\n    ";
            }

            FileEditor::replaceInFile(
                $viteConfigPath,
                'plugins: [' . $matches[1] . ']',
                'plugins: [' . $replacement . ']'
            );
        } else {
            throw new \RuntimeException("Could not find plugins array in vite config file");
        }
    }

    /**
     * Update Laravel plugin input array to include flexilla.js
     *
     * @param string $viteConfigPath Path to the vite config file
     */
    private static function updateLaravelPluginInput(string $viteConfigPath): void
    {
        $content = file_get_contents($viteConfigPath);

        // Check if flexilla.js is already in the input array
        if (FileEditor::fileContains($viteConfigPath, 'flexilla.js')) {
            return; // Already included
        }

        // Find the Laravel plugin input array
        if (preg_match('/laravel\s*\(\s*\{\s*input:\s*\[(.*?)\]/s', $content, $matches)) {
            $inputContent = trim($matches[1]);

            // Add flexilla.js to the input array
            $flexillaEntry = "'resources/js/flexilla.js'";

            if ($inputContent !== '') {
                // Add flexilla.js after existing entries
                $replacement = $inputContent . ",\n                " . $flexillaEntry;
            } else {
                // Add flexilla.js as the first entry
                $replacement = "\n                " . $flexillaEntry . "\n            ";
            }

            FileEditor::replaceInFile(
                $viteConfigPath,
                'input: [' . $matches[1] . ']',
                'input: [' . $replacement . ']'
            );
        } else {
            throw new \RuntimeException("Could not find Laravel plugin input array in vite config file");
        }
    }

    /**
     * Find the UnoCSS configuration file (uno.config.js or uno.config.ts)
     *
     * @return string|null Path to the uno config file or null if not found
     */
    private static function findUnoConfigFile(): ?string
    {
        $possibleFiles = [
            getcwd() . '/uno.config.js',
            getcwd() . '/uno.config.ts'
        ];

        foreach ($possibleFiles as $file) {
            if (file_exists($file)) {
                return $file;
            }
        }

        return null;
    }

    /**
     * Add UnoCSS import to the vite config file
     *
     * @param string $viteConfigPath Path to the vite config file
     */
    private static function addUnoImport(string $viteConfigPath): void
    {
        $unoImport = "import UnoCSS from 'unocss/vite';";

        // Check if import already exists
        if (FileEditor::fileContains($viteConfigPath, $unoImport)) {
            return; // Import already exists
        }

        // Check for alternative import formats
        if (FileEditor::fileContains($viteConfigPath, "from 'unocss/vite'")) {
            return; // Import already exists in some form
        }

        // Find the position to insert the import (after other imports)
        $content = file_get_contents($viteConfigPath);
        $lines = explode("\n", $content);

        $insertPosition = 0;
        foreach ($lines as $index => $line) {
            if (strpos(trim($line), 'import') === 0) {
                $insertPosition = $index + 1;
            } elseif (trim($line) === '' && $insertPosition > 0) {
                // Found empty line after imports
                break;
            } elseif (strpos(trim($line), 'export') === 0 || strpos(trim($line), 'const') === 0) {
                // Found start of actual code
                break;
            }
        }

        FileEditor::insertCode($viteConfigPath, $unoImport, 'after_line', $insertPosition);
    }

    /**
     * Add UnoCSS plugin to the plugins array in vite config
     *
     * @param string $viteConfigPath Path to the vite config file
     */
    private static function addUnoPlugin(string $viteConfigPath): void
    {
        // Check if UnoCSS() is already in the plugins array
        if (FileEditor::fileContains($viteConfigPath, 'UnoCSS()')) {
            return; // Plugin already exists
        }

        $content = file_get_contents($viteConfigPath);

        // Find the plugins array and add UnoCSS() to it
        if (preg_match('/plugins:\s*\[(.*?)\]/s', $content, $matches)) {
            $pluginsContent = trim($matches[1]);

            // Check if there are already plugins
            if ($pluginsContent !== '') {
                // Add UnoCSS() after existing plugins
                $replacement = $pluginsContent . ",\n        UnoCSS(),";
            } else {
                // Add UnoCSS() as the first plugin
                $replacement = "\n        UnoCSS(),\n    ";
            }

            FileEditor::replaceInFile(
                $viteConfigPath,
                'plugins: [' . $matches[1] . ']',
                'plugins: [' . $replacement . ']'
            );
        } else {
            throw new \RuntimeException("Could not find plugins array in vite config file");
        }
    }
}
