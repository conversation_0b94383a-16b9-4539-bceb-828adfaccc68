<?php

namespace Flexiwind\Command;

use <PERSON>lex<PERSON><PERSON>\Core\ProjectDetector;
use <PERSON>lexi<PERSON>\Core\ConfigWriter;
use <PERSON>lexiwind\Core\FileGenerator;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use function Laravel\Prompts\{text, info, warning, note, error, confirm, suggest, select, spin};



class InitCommand extends Command
{
    protected static $defaultName = 'init';

    protected function configure(): void
    {
        $this
            ->setName('init')
            ->setDescription('Initialize Flexiwind in your project')
            ->addOption(
                'path',
                'p',
                InputOption::VALUE_OPTIONAL,
                'Target directory where files will be generated',
                getcwd()
            )
            ->addOption('new-laravel', 'nl', InputOption::VALUE_NONE, 'Create a new Laravel project and initialize')
            ->addOption('new-symfony', 'ns', InputOption::VALUE_NONE, 'Create a new Symfony project and initialize')
            ->addOption('tailwind', null, InputOption::VALUE_NONE, 'Use tailwindcss as styling framework')
            ->addOption('uno', null, InputOption::VALUE_NONE, 'Use UnoCSS as styling framework');
    }

    private static function createNewLaravel()
    {
        note('🚀 Creating a new Laravel project...');
        $name = suggest('What is the name of your project?', ['my-app'], 'my-app', 'my-app');
        spin(
            callback: fn() => exec('composer create-project laravel/laravel ' . $name),
            message: 'Creating a new laravel project'
        );
        chdir($name);
    }

    private static function createNewSymfony()
    {
        note('🚀 Creating a new Symfony project..');
        $name = suggest('What is the name of your project?', ['my-app'], 'my-app', 'my-app');
        spin(
            callback: fn() => exec('composer create-project symfony/skeleton ' . $name),
            message: 'Creating a new Symfony project'
        );
        chdir($name);
    }

    private static function createNewPhpVite()
    {
        note('🚀 Creating a new Symfony project..');
        $name = suggest('What is the name of your project?', ['my-app'], 'my-app', 'my-app');
        spin(
            callback: fn() => exec('composer create-project symfony/skeleton ' . $name),
            message: 'Creating a new Symfony project'
        );
        chdir($name);
    }

    private static function initTheming()
    {
        $cssFramework = select(
            label: '🎨 Which Styling Framework would you like to use?',
            options: ['tailwindcss', 'unocss'],
            default: 'tailwindcss',
        );
        $theme = select(
            label: '🎨 Which theme would you like to use?',
            options: ['flexiwind', 'water', 'earth', 'fire', 'air'],
            default: 'flexiwind',
        );
        $alpine = confirm('⚡ Are you using Alpine?');

        return [
            'theme' => $theme,
            'cssFramework' => $cssFramework,
            'alpine' => $alpine
        ];
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $helper = $this->getHelper('question');
        assert($helper instanceof \Symfony\Component\Console\Helper\QuestionHelper);

        note('⚡ Flexiwind Initializer');

        $answers = [];
        $initProjectFromCli = false;


        if ($input->getOption('new-laravel')) {
            self::createNewLaravel();
            $initProjectFromCli = true;
        } elseif ($input->getOption('new-symfony')) {
            self::createNewSymfony();
            $initProjectFromCli = true;
        }

        if ($input->getOption('tailwind') && !$input->getOption('uno')) {
            $answers['cssFramework'] = 'tailwindcss';
        } elseif ($input->getOption('uno') && !$input->getOption('tailwind')) {
            $answers['cssFramework'] = 'unocss';
        }


        $hasComposer = ProjectDetector::check_Composer(getcwd());
        if (!$hasComposer) {
            warning('❌ No composer.json found. Please run `composer init` first or init a new project from following command.');
            $framework = select(
                label: 'What framework are you using?',
                options: ['laravel', 'symfony', 'PHP + vite', 'none'],
                default: 'laravel',
            );
            if ($framework == 'none') {
                error('❌ No framework selected. Please run `composer init` first.');
                return Command::FAILURE;
            }

            if ($framework == 'laravel') {
                self::createNewLaravel();
                $initProjectFromCli = true;
            } elseif ($framework == 'symfony') {
                self::createNewSymfony();
                $initProjectFromCli = true;
            } else {
                self::createNewPhpVite();
                $initProjectFromCli = true;
            }
        }


        $projectType = ProjectDetector::detect(getcwd());
        $packageManager = ProjectDetector::getNodePackageManager(getcwd());

        if (!$initProjectFromCli) note("🔍 Detected project: '$projectType'");

        $info = self::initTheming();
        $answers['alpine'] = $info['alpine'];
        $answers['theme'] = $info['theme'];
        $answers['cssFramework'] = $info['cssFramework'];

        $answers['css'] = text(
            label: "Where do you want to place your main CSS files",
            placeholder: 'resources/css',
            default: 'resources/css'
        );
        $answers['js'] = text(
            label: 'Where do you want to place your JS files',
            placeholder: 'resources/js',
            default: 'resources/js'
        );


        spin(
            callback: fn() => ([
                ConfigWriter::createFlexiwindYaml($answers),
                ConfigWriter::createKeysYaml()
            ]),
            message: "Creating flexiwind config file"
        );
        info('Config file created');
        spin(
            callback: fn() => FileGenerator::generateBaseFiles($projectType, $answers),
            message: "Creating base files"
        );


        info('Base files created');



        info('✅ Flexiwind initialization complete!');

        return Command::SUCCESS;
    }
}
