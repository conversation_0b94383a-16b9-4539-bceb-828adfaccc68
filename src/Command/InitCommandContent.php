<?php

namespace Flexiwind\Command;

/**
 * This file contains the code content that will be integrated into InitCommand
 * Organized by sections for easy integration
 */
class InitCommandContent
{
    /**
     * Laravel Helper Class Content
     */
    public static function getLaravelHelpersContent(): string
    {
        return <<<'PHP'
<?php

namespace App\Flexiwind;

class Helpers 
{
    /**
     * Generate Flexiwind component classes
     */
    public static function component(string $name, array $variants = []): string
    {
        $baseClasses = self::getComponentClasses($name);
        $variantClasses = self::getVariantClasses($name, $variants);
        
        return trim($baseClasses . ' ' . $variantClasses);
    }
    
    /**
     * Get base component classes
     */
    private static function getComponentClasses(string $name): string
    {
        $components = [
            'button' => 'px-4 py-2 rounded font-medium transition-colors',
            'card' => 'bg-white rounded-lg shadow-md p-6',
            'input' => 'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2',
            'badge' => 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
        ];
        
        return $components[$name] ?? '';
    }
    
    /**
     * Get variant classes
     */
    private static function getVariantClasses(string $name, array $variants): string
    {
        $variantMap = [
            'button' => [
                'primary' => 'bg-blue-600 text-white hover:bg-blue-700',
                'secondary' => 'bg-gray-200 text-gray-900 hover:bg-gray-300',
                'danger' => 'bg-red-600 text-white hover:bg-red-700',
                'small' => 'px-2 py-1 text-sm',
                'large' => 'px-6 py-3 text-lg',
            ],
            'card' => [
                'elevated' => 'shadow-lg',
                'bordered' => 'border border-gray-200',
            ],
            'input' => [
                'error' => 'border-red-500 focus:ring-red-500',
                'success' => 'border-green-500 focus:ring-green-500',
            ],
            'badge' => [
                'primary' => 'bg-blue-100 text-blue-800',
                'success' => 'bg-green-100 text-green-800',
                'warning' => 'bg-yellow-100 text-yellow-800',
                'danger' => 'bg-red-100 text-red-800',
            ],
        ];
        
        $classes = [];
        foreach ($variants as $variant) {
            if (isset($variantMap[$name][$variant])) {
                $classes[] = $variantMap[$name][$variant];
            }
        }
        
        return implode(' ', $classes);
    }
    
    /**
     * Legacy hello method for backward compatibility
     */
    public static function hello(): string
    {
        return "Hello from Flexiwind!";
    }
}
PHP;
    }

    /**
     * Custom PHP Helpers Content
     */
    public static function getCustomHelpersContent(): string
    {
        return <<<'PHP'
<?php

/**
 * Flexiwind Helper Functions
 */

if (!function_exists('flexiwind_component')) {
    function flexiwind_component(string $name, array $variants = []): string
    {
        $baseClasses = flexiwind_get_component_classes($name);
        $variantClasses = flexiwind_get_variant_classes($name, $variants);
        
        return trim($baseClasses . ' ' . $variantClasses);
    }
}

if (!function_exists('flexiwind_get_component_classes')) {
    function flexiwind_get_component_classes(string $name): string
    {
        $components = [
            'button' => 'px-4 py-2 rounded font-medium transition-colors',
            'card' => 'bg-white rounded-lg shadow-md p-6',
            'input' => 'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2',
            'badge' => 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
        ];
        
        return $components[$name] ?? '';
    }
}

if (!function_exists('flexiwind_get_variant_classes')) {
    function flexiwind_get_variant_classes(string $name, array $variants): string
    {
        $variantMap = [
            'button' => [
                'primary' => 'bg-blue-600 text-white hover:bg-blue-700',
                'secondary' => 'bg-gray-200 text-gray-900 hover:bg-gray-300',
                'danger' => 'bg-red-600 text-white hover:bg-red-700',
                'small' => 'px-2 py-1 text-sm',
                'large' => 'px-6 py-3 text-lg',
            ],
            'card' => [
                'elevated' => 'shadow-lg',
                'bordered' => 'border border-gray-200',
            ],
            'input' => [
                'error' => 'border-red-500 focus:ring-red-500',
                'success' => 'border-green-500 focus:ring-green-500',
            ],
            'badge' => [
                'primary' => 'bg-blue-100 text-blue-800',
                'success' => 'bg-green-100 text-green-800',
                'warning' => 'bg-yellow-100 text-yellow-800',
                'danger' => 'bg-red-100 text-red-800',
            ],
        ];
        
        $classes = [];
        foreach ($variants as $variant) {
            if (isset($variantMap[$name][$variant])) {
                $classes[] = $variantMap[$name][$variant];
            }
        }
        
        return implode(' ', $classes);
    }
}

if (!function_exists('flexiwind_hello')) {
    function flexiwind_hello(): string
    {
        return 'Hello from Flexiwind!';
    }
}
PHP;
    }

    /**
     * JavaScript Content for Flexilla
     */
    public static function getFlexillaJsContent(): string
    {
        return <<<'JS'


JS;
    }

    /**
     * Base Flexiwind CSS Content
     */
    public static function getFlexiwindCssContent(): string
    {
        return <<<'CSS'

CSS;
    }

    /**
     * Button Component CSS Content
     */
    public static function getButtonCssContent(): string
    {
        return <<<'CSS'
/**
 * Flexiwind Button Styles
 * Comprehensive button utilities
 */


CSS;
    }

    /**
     * Utility Classes CSS Content
     */
    public static function getUtilitiesCssContent(): string
    {
        return <<<'CSS'
/**
 * Flexiwind Utility Classes
 * Additional utility classes for common styling needs
 */

CSS;
    }
}
