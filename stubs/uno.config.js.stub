import { defineConfig, presetUno, presetAttributify, presetTypography } from 'unocss'

export default defineConfig({
  presets: [
    presetUno(),
    presetAttributify(),
    presetTypography(),
  ],
  content: {
    filesystem: [
      'resources/**/*.{php,blade.php,js,ts,vue}',
      'app/**/*.php',
      'resources/views/**/*.blade.php',
    ]
  },
  theme: {
    colors: {
      primary: {
        50: '#eff6ff',
        100: '#dbeafe',
        200: '#bfdbfe',
        300: '#93c5fd',
        400: '#60a5fa',
        500: '#3b82f6',
        600: '#2563eb',
        700: '#1d4ed8',
        800: '#1e40af',
        900: '#1e3a8a',
      }
    }
  },
  shortcuts: {
    'btn': 'px-4 py-2 rounded inline-block bg-primary-500 text-white cursor-pointer hover:bg-primary-600 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50',
    'btn-primary': 'btn bg-primary-500 hover:bg-primary-600',
    'btn-secondary': 'btn bg-gray-500 hover:bg-gray-600',
    'btn-success': 'btn bg-green-500 hover:bg-green-600',
    'btn-danger': 'btn bg-red-500 hover:bg-red-600',
    'btn-warning': 'btn bg-yellow-500 hover:bg-yellow-600',
    'btn-info': 'btn bg-blue-500 hover:bg-blue-600',
  },
  rules: [
    // Custom rules for Flexiwind compatibility
    [/^flexiwind-(.+)$/, ([, d]) => ({ [`--flexiwind-${d}`]: '1' })],
  ]
})
