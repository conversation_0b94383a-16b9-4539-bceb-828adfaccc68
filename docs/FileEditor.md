# FileEditor Class Documentation

The `FileEditor` class provides comprehensive functionality for editing existing files or creating new ones with content injection capabilities.

## Features

- ✅ Create new files with content or update existing ones
- ✅ Insert code at specific positions (top, bottom, after/before specific lines)
- ✅ Search-based code insertion using patterns
- ✅ Append content only if it doesn't already exist
- ✅ Replace content with regex or string matching
- ✅ Check if files contain specific content
- ✅ Automatic directory creation
- ✅ Comprehensive error handling

## Methods

### `updateFileContent(string $filePath, string $content, bool $createIfNotExists = true): bool`

Updates or creates a file with the specified content.

**Parameters:**
- `$filePath`: Path to the file
- `$content`: Content to write to the file
- `$createIfNotExists`: Whether to create the file if it doesn't exist

**Example:**
```php
FileEditor::updateFileContent('config/app.php', $newConfig);
```

### `insertCode(string $filePath, string $code, string $position = 'bottom', ?int $lineNumber = null, ?string $searchPattern = null, bool $createIfNotExists = true): bool`

Inserts code into a file at a specific position.

**Parameters:**
- `$filePath`: Path to the file
- `$code`: Code to insert
- `$position`: Position where to insert ('top', 'bottom', 'after_line', 'before_line')
- `$lineNumber`: Line number for line-specific operations (1-based)
- `$searchPattern`: Pattern to search for when using line-specific operations
- `$createIfNotExists`: Whether to create the file if it doesn't exist

**Examples:**
```php
// Insert at top
FileEditor::insertCode('app.js', '// Header comment', 'top');

// Insert at bottom
FileEditor::insertCode('app.js', '// Footer comment', 'bottom');

// Insert after specific line number
FileEditor::insertCode('app.js', 'console.log("debug");', 'after_line', 5);

// Insert after finding a pattern
FileEditor::insertCode('app.js', 'console.log("found it");', 'after_line', null, 'import React');
```

### `appendIfNotExists(string $filePath, string $content, bool $createIfNotExists = true): bool`

Appends content to a file only if it doesn't already exist.

**Example:**
```php
FileEditor::appendIfNotExists('.gitignore', '.flexiwind/');
```

### `replaceInFile(string $filePath, string $search, string $replacement, bool $useRegex = false, bool $replaceAll = true): bool`

Replaces content in a file using string or regex matching.

**Examples:**
```php
// Simple string replacement
FileEditor::replaceInFile('config.js', 'oldValue', 'newValue');

// Regex replacement
FileEditor::replaceInFile('config.js', '/old.*value/i', 'newValue', true);
```

### `fileContains(string $filePath, string $content, bool $useRegex = false): bool`

Checks if a file contains specific content.

**Example:**
```php
if (FileEditor::fileContains('package.json', 'tailwindcss')) {
    echo "TailwindCSS is already installed";
}
```

## Vite Configuration Updates

The `ConfigWriter::updateTailwindViteConfig()` method automatically updates your Vite configuration to work with Flexiwind and TailwindCSS.

### What it does:

1. **Finds the Vite config file** (`vite.config.js` or `vite.config.ts`)
2. **Adds TailwindCSS import**: `import tailwindcss from '@tailwindcss/vite';`
3. **Adds TailwindCSS plugin** to the plugins array: `tailwindcss()`
4. **Updates Laravel plugin input** to include `'resources/js/flexilla.js'`

### Before:
```javascript
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css', 
                'resources/js/app.js'
            ],
            refresh: true,
        }),
    ],
});
```

### After:
```javascript
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css', 
                'resources/js/app.js',
                'resources/js/flexilla.js'
            ],
            refresh: true,
        }),
        tailwindcss(),
    ],
});
```

### Usage:
```php
ConfigWriter::updateTailwindViteConfig();
```

## UnoCSS Configuration Updates

The Flexiwind CLI also provides comprehensive UnoCSS support with two dedicated methods:

### `ConfigWriter::updateUnoConfig()`

**Purpose**: Creates or completely replaces the UnoCSS configuration file with a Flexiwind-optimized setup.

**What it does**:
1. **Finds existing config** (`uno.config.js` or `uno.config.ts`) or creates new `uno.config.js`
2. **Replaces entire configuration** with Flexiwind-optimized UnoCSS config
3. **Includes presets**: UnoCSS core, Attributify, and Typography presets
4. **Adds Flexiwind theme**: Custom color palette and shortcuts
5. **Configures content paths**: Optimized for Laravel/PHP projects

**Generated uno.config.js includes**:
- UnoCSS core presets (Uno, Attributify, Typography)
- Custom Flexiwind color theme
- Button shortcuts (btn, btn-primary, btn-secondary, etc.)
- Content scanning for PHP, Blade, JS, TS, Vue files
- Custom Flexiwind compatibility rules

### `ConfigWriter::updateUnoViteConfig()`

**Purpose**: Updates Vite configuration to work with UnoCSS and Flexiwind.

**What it does**:
1. **Finds the Vite config file** (`vite.config.js` or `vite.config.ts`)
2. **Adds UnoCSS import**: `import UnoCSS from 'unocss/vite';`
3. **Adds UnoCSS plugin** to the plugins array: `UnoCSS()`
4. **Updates Laravel plugin input** to include `'resources/js/flexilla.js'`

### Before (UnoCSS):
```javascript
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js'
            ],
            refresh: true,
        }),
    ],
});
```

### After (UnoCSS):
```javascript
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import UnoCSS from 'unocss/vite';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                'resources/js/flexilla.js'
            ],
            refresh: true,
        }),
        UnoCSS(),
    ],
});
```

### Usage:
```php
// Create/replace UnoCSS configuration
ConfigWriter::updateUnoConfig();

// Update Vite config for UnoCSS
ConfigWriter::updateUnoViteConfig();
```

## Error Handling

All methods throw `\RuntimeException` with descriptive messages when operations fail:

- File not found (when `$createIfNotExists` is false)
- Directory creation failures
- File read/write failures
- Invalid regex patterns
- Missing required patterns in config files

## Safety Features

- **Idempotent operations**: Running the same operation multiple times won't cause duplicates
- **Pattern detection**: Checks for existing imports/plugins before adding them (TailwindCSS/UnoCSS)
- **Automatic directory creation**: Creates parent directories as needed
- **Content validation**: Verifies content exists before attempting operations
- **Configuration replacement**: UnoCSS config is completely replaced to ensure consistency
- **Framework detection**: Automatically detects and works with existing Vite configurations
