# CSS Framework Configuration Comparison

This document compares how Flexiwind CLI handles TailwindCSS vs UnoCSS configuration.

## Method Comparison

| Aspect | TailwindCSS | UnoCSS |
|--------|-------------|---------|
| **Vite Config Method** | `updateTailwindViteConfig()` | `updateUnoViteConfig()` |
| **Config File Method** | ❌ Not implemented | `updateUnoConfig()` |
| **Import Statement** | `import tailwindcss from '@tailwindcss/vite';` | `import UnoCSS from 'unocss/vite';` |
| **Plugin Call** | `tailwindcss()` | `UnoCSS()` |
| **Config File** | Uses existing `tailwind.config.js` | Replaces `uno.config.js` completely |
| **Configuration Strategy** | Preserves existing config | Complete replacement with Flexiwind optimizations |

## Vite Configuration Updates

### TailwindCSS Vite Integration
```javascript
// Added import
import tailwindcss from '@tailwindcss/vite';

// Added plugin
plugins: [
    laravel({...}),
    tailwindcss(), // ← Added
]
```

### UnoCSS Vite Integration
```javascript
// Added import  
import UnoCSS from 'unocss/vite';

// Added plugin
plugins: [
    laravel({...}),
    UnoCSS(), // ← Added
]
```

## Configuration File Handling

### TailwindCSS
- **No automatic config generation** - relies on existing `tailwind.config.js`
- **Preserves user customizations** in existing config
- **Manual setup required** for initial configuration

### UnoCSS
- **Automatic config generation** - creates optimized `uno.config.js`
- **Complete replacement strategy** - ensures consistency
- **Flexiwind-optimized defaults** included out of the box

## Generated UnoCSS Configuration Features

The `updateUnoConfig()` method creates a comprehensive UnoCSS configuration with:

### Presets
- `presetUno()` - Core UnoCSS utilities
- `presetAttributify()` - Attributify mode support
- `presetTypography()` - Typography utilities

### Content Scanning
```javascript
content: {
  filesystem: [
    'resources/**/*.{php,blade.php,js,ts,vue}',
    'app/**/*.php',
    'resources/views/**/*.blade.php',
  ]
}
```

### Custom Theme
- **Primary color palette** (50-900 shades)
- **Flexiwind-compatible colors**

### Shortcuts (Button System)
```javascript
shortcuts: {
  'btn': 'px-4 py-2 rounded inline-block bg-primary-500 text-white cursor-pointer hover:bg-primary-600 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50',
  'btn-primary': 'btn bg-primary-500 hover:bg-primary-600',
  'btn-secondary': 'btn bg-gray-500 hover:bg-gray-600',
  'btn-success': 'btn bg-green-500 hover:bg-green-600',
  'btn-danger': 'btn bg-red-500 hover:bg-red-600',
  'btn-warning': 'btn bg-yellow-500 hover:bg-yellow-600',
  'btn-info': 'btn bg-blue-500 hover:bg-blue-600',
}
```

### Custom Rules
```javascript
rules: [
  // Custom rules for Flexiwind compatibility
  [/^flexiwind-(.+)$/, ([, d]) => ({ [`--flexiwind-${d}`]: '1' })],
]
```

## Usage Examples

### For TailwindCSS Projects
```php
// Only updates Vite configuration
ConfigWriter::updateTailwindViteConfig();
```

### For UnoCSS Projects
```php
// Step 1: Create/replace UnoCSS configuration
ConfigWriter::updateUnoConfig();

// Step 2: Update Vite configuration
ConfigWriter::updateUnoViteConfig();
```

## Key Differences Summary

1. **Configuration Philosophy**:
   - **TailwindCSS**: Minimal intervention, preserves existing setup
   - **UnoCSS**: Complete configuration management, opinionated defaults

2. **File Management**:
   - **TailwindCSS**: Only touches Vite config
   - **UnoCSS**: Manages both Vite config and UnoCSS config file

3. **Customization**:
   - **TailwindCSS**: User maintains their own config file
   - **UnoCSS**: Flexiwind provides optimized configuration

4. **Setup Complexity**:
   - **TailwindCSS**: Requires existing config file
   - **UnoCSS**: Zero-config setup, works out of the box

## Best Practices

### When to Use TailwindCSS Method
- Existing TailwindCSS projects with custom configurations
- Teams that prefer manual configuration control
- Projects with complex TailwindCSS customizations

### When to Use UnoCSS Method
- New projects starting with UnoCSS
- Teams that prefer opinionated, optimized defaults
- Projects that want Flexiwind's full UnoCSS integration

Both methods ensure that `resources/js/flexilla.js` is properly included in the Vite build process for full Flexiwind functionality.
